<% var block = options.block %>
<%- block._editable %>

<%
	if (!page.data.arlo_template_codes_for_skill_courses) {
		console.error(`Error rendering page: ${page.slug}\nCourse Banner block: \'page.data.arlo_template_codes_for_skill_courses\' is required for Arlo components but is missing.`);
	}
%>

<div class="relative pb-20 max-lg:overflow-hidden">
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 417 457" fill="none" class="absolute -top-[2px] -right-[2px] fill-primary opacity-20 z-[1] w-[200px] lg:w-[417px]">
    <path d="M337 450.9H336.8C336.7 450.9 323.9 454.1 315.3 454.1C306.7 454.1 297.6 451.7 294.7 450.9H294.6C203.3 425.2 131.7 381.6 81.8 321.2C31.9 261.1 5.4 186.1 2.8 98.4C2.8 97.6 2.8 81.2 2.9 51.3L3.1 2H1.1L0.9 51.3C0.8 82.2 0.7 97.7 0.8 98.5C3.4 186.6 30.1 262 80.2 322.6C130.4 383.3 202.3 427.1 294 452.9H294.1C297.1 453.7 307.5 456.2 315.2 456.2C322.9 456.2 335.7 453.3 337.1 453H337.4C365.5 445.1 392.1 435 416.4 423L415.5 421.2C391.3 433.1 364.9 443.2 337 451V450.9Z" fill="#510C76"/>
    <path d="M296.8 381.6C298.6 382.1 307.8 384.4 314.7 384.4C321.6 384.4 331.9 382 333.1 381.7H333.4C363.2 373.3 390.9 362 415.7 348.1L414.7 346.4C390.1 360.2 362.6 371.4 333 379.7H332.8C332.7 379.7 321.9 382.4 314.7 382.4C307.5 382.4 299.1 380.1 297.3 379.7C144.7 339 57.7 233.8 52.1 83.5C52.1 82.9 52.1 68.2 52.3 1H50.3C50.1 54.9 50.1 82.7 50.1 83.6C55.7 234.9 143.2 340.7 296.7 381.6H296.8Z" fill="#510C76"/>
    <path d="M299.6 310.4C299.9 310.4 308.2 312.7 314.2 312.7C320.2 312.7 327.7 310.8 329.2 310.4H329.4C361.7 301.3 390.7 288.1 415.6 271.1L414.5 269.4C389.8 286.2 361.1 299.3 329 308.3H328.7C328.2 308.5 319.8 310.5 314.2 310.5C308.6 310.5 300.5 308.4 300.2 308.3C176.6 275.3 106.1 190.2 101.7 68.4C101.7 67.9 101.7 55.8 101.8 0.8H99.8C99.8 45 99.7 67.7 99.7 68.4C104.2 191.1 175.2 276.9 299.7 310.1L299.6 310.4Z" fill="#510C76"/>
    <path d="M302.4 239.2C306 240.2 309.8 240.7 313.6 240.9C317.5 240.7 321.3 240.1 325 239.2H325.2C361.8 228.9 392.6 211.8 416.6 188.4L415.2 187C391.4 210.1 361 227 324.7 237.2H324.5C320.9 238.1 317.1 238.7 313.4 238.9C309.8 238.7 306.2 238.2 302.7 237.2C208.1 212 154.2 146.8 150.8 53.6C150.8 53 150.8 35.3 150.8 0.9H148.8C148.8 35.9 148.8 53.1 148.8 53.6C152.3 147.7 206.7 213.6 302.2 239L302.4 239.2Z" fill="#510C76"/>
    <path d="M305.2 167.8C307.8 168.5 310.4 168.8 313.1 169C315.8 168.9 318.5 168.5 321.2 167.8C355.8 158.1 382.7 139.8 401 113.5C407.4 104.1 412.7 94 416.6 83.4L414.7 82.7C410.8 93.2 405.6 103.2 399.3 112.4C381.3 138.3 354.8 156.3 320.7 165.9C318.2 166.5 315.6 166.9 313.1 167C310.6 166.9 308.1 166.5 305.7 165.9C239.6 147.3 202.2 102.1 200.4 38.7C200.4 38.2 200.4 25.5 200.4 1.1H198.4C198.4 34.4 198.4 38.6 198.4 38.8C200.2 103.2 238.2 149.1 305.2 167.9V167.8Z" fill="#510C76"/>
  </svg>
  <div class="relative">
    <div class="container mx-auto !grid lg:grid-cols-[1fr_348px] lg:gap-[142px]">
      <div>
        <div class="flex flex-col items-start py-[72px] pb-[72px] lg:py-[125px] lg:pb-[90px] bg-lilac relative before:content-[''] after:content-[''] before:absolute after:absolute before:-left-full after:-right-full before:top-0 after:top-0 before:bg-lilac before:h-full after:bg-lilac before:w-[1000%] after:w-[1000%] after:h-full after:-z-[1] before:-z-[1]">
          <div class="text-[#510C76] text-xs font-bold z-[1] font-['Adelle_Sans'] bg-[#81EEBE] mb-3 leading-tight grid grid-cols-[auto_1fr] gap-[5px] items-center px-[22px] py-[7px] rounded-[10rem]">
            Skill Sprint
            <svg role="presentation" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 fill-[#510C76]">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M12 7C11.7348 7 11.4804 6.89464 11.2929 6.70711C11.1054 6.51957 11 6.26522 11 6C11 5.73478 11.1054 5.48043 11.2929 5.29289C11.4804 5.10536 11.7348 5 12 5H17C17.2652 5 17.5196 5.10536 17.7071 5.29289C17.8946 5.48043 18 5.73478 18 6V11C18 11.2652 17.8946 11.5196 17.7071 11.7071C17.5196 11.8946 17.2652 12 17 12C16.7348 12 16.4804 11.8946 16.2929 11.7071C16.1054 11.5196 16 11.2652 16 11V8.414L11.707 12.707C11.5195 12.8945 11.2652 12.9998 11 12.9998C10.7348 12.9998 10.4805 12.8945 10.293 12.707L8 10.414L3.707 14.707C3.51839 14.8892 3.26579 14.99 3.00359 14.9877C2.7414 14.9854 2.49058 14.8802 2.30518 14.6948C2.11977 14.5094 2.0146 14.2586 2.01232 13.9964C2.01004 13.7342 2.11084 13.4816 2.293 13.293L7.293 8.293C7.48052 8.10553 7.73483 8.00021 8 8.00021C8.26516 8.00021 8.51947 8.10553 8.707 8.293L11 10.586L14.586 7H12Z" fill=""/>
            </svg>
          </div>
          <h1 class="text-white text-4xl font-adelle-semibold leading-10 mb-0 z-[1]"><%- block.heading || page.data.title %> </h1>
          <div class="[&_*]:text-white [&_p]:mb-0 [&_p]:leading-[1.4] [&_p]:text-base  font-normal font-['Adelle_Sans'] mt-6 leading-normal">
            <%- plugins.richText(block.description) %>
          </div>
          <% if (block.cta_link && block.cta_text) { %>
          <a href="<%= block.cta_link.cached_url %>" class="text-white mt-9 bg-primary px-[42px] py-6 rounded-sm font-bold text-base no-underline transition-all duration-200 hover:bg-primary/50"> 
            <%= block.cta_text %>
          </a>
          <% } %>
          <%- plugins.include('snippets/create-skill-course-banner-box.html', { classes: 'block lg:hidden mt-10 max-w-[348px]', bullets: block.sticky_box_text.split('\n'), arlo_template_codes_for_skill_courses: page.data.arlo_template_codes_for_skill_courses }) %>
        </div>
        <%- plugins.include('snippets/creative-skill-course-banner-intakes.html', {
          classes: 'hidden lg:block mt-12 lg:mt-[107px]',
          heading: block.intakes_heading,
          description: block.intakes_description,
          footer: block.intakes_footer,
          arlo_template_codes_for_skill_courses: page.data.arlo_template_codes_for_skill_courses
        }) %>
      </div>
      <div>
        <%- plugins.include('snippets/creative-skill-course-banner-intakes.html', {
          classes: 'block lg:hidden mt-20',
          heading: block.intakes_heading,
          description: block.intakes_description,
          footer: block.intakes_footer,
          arlo_template_codes_for_skill_courses: page.data.arlo_template_codes_for_skill_courses
        }) %>
        <%- plugins.include('snippets/create-skill-course-banner-box.html', { classes: 'hidden lg:block lg:mt-[125px] lg:sticky lg:top-[10px] z-[1]', bullets: block.sticky_box_text.split('\n'), arlo_template_codes_for_skill_courses: page.data.arlo_template_codes_for_skill_courses }) %>
      </div>
    </div>
  </div>
</div>